/**
 * إصلاح شامل لنظام حجز الأطباء
 * يحل جميع المشاكل المتعلقة بالتنقل والبيانات والتفاعل
 */

// بيانات الأطباء الموحدة
const DOCTORS_DATABASE = {
    1: { name: 'د. أحمد محمد', image: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=150&h=150&fit=crop&crop=face', price: 300, specialty: 'أمراض القلب', rating: 4.8, experience: 15 },
    2: { name: 'د. فاطمة علي', image: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=150&h=150&fit=crop&crop=face', price: 350, specialty: 'أمراض القلب', rating: 4.9, experience: 12 },
    3: { name: 'د. محمد حسن', image: 'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=150&h=150&fit=crop&crop=face', price: 400, specialty: 'أمراض القلب', rating: 4.7, experience: 18 },
    4: { name: 'د. سارة أحمد', image: 'https://images.unsplash.com/photo-1594824475317-d3c5b8b8e8e8?w=150&h=150&fit=crop&crop=face', price: 450, specialty: 'أمراض الأعصاب', rating: 4.9, experience: 20 },
    5: { name: 'د. عمر محمود', image: 'https://images.unsplash.com/photo-1607990281513-2c110a25bd8c?w=150&h=150&fit=crop&crop=face', price: 380, specialty: 'أمراض الأعصاب', rating: 4.6, experience: 14 },
    6: { name: 'د. ليلى حسام', image: 'https://images.unsplash.com/photo-1594824475317-d3c5b8b8e8e8?w=150&h=150&fit=crop&crop=face', price: 320, specialty: 'العظام', rating: 4.8, experience: 16 },
    7: { name: 'د. كريم فؤاد', image: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=150&h=150&fit=crop&crop=face', price: 280, specialty: 'العظام', rating: 4.7, experience: 11 },
    8: { name: 'د. نور الهدى', image: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=150&h=150&fit=crop&crop=face', price: 250, specialty: 'الجلدية', rating: 4.9, experience: 13 },
    9: { name: 'د. حسام الدين', image: 'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=150&h=150&fit=crop&crop=face', price: 220, specialty: 'الجلدية', rating: 4.5, experience: 9 },
    10: { name: 'د. منى سالم', image: 'https://images.unsplash.com/photo-1594824475317-d3c5b8b8e8e8?w=150&h=150&fit=crop&crop=face', price: 200, specialty: 'طب الأطفال', rating: 4.8, experience: 17 },
    11: { name: 'د. يوسف عادل', image: 'https://images.unsplash.com/photo-1607990281513-2c110a25bd8c?w=150&h=150&fit=crop&crop=face', price: 180, specialty: 'طب الأطفال', rating: 4.6, experience: 8 },
    12: { name: 'د. رانيا طارق', image: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=150&h=150&fit=crop&crop=face', price: 150, specialty: 'طب عام', rating: 4.7, experience: 10 },
    13: { name: 'د. خالد نبيل', image: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=150&h=150&fit=crop&crop=face', price: 120, specialty: 'طب عام', rating: 4.4, experience: 7 }
};

// بيانات التخصصات
const SPECIALTIES_DATABASE = {
    cardiology: { name: 'أمراض القلب', doctors: [1, 2, 3] },
    neurology: { name: 'أمراض الأعصاب', doctors: [4, 5] },
    orthopedics: { name: 'العظام', doctors: [6, 7] },
    dermatology: { name: 'الجلدية', doctors: [8, 9] },
    pediatrics: { name: 'طب الأطفال', doctors: [10, 11] },
    general: { name: 'طب عام', doctors: [12, 13] }
};

// الأوقات المتاحة
const AVAILABLE_TIMES = [
    '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
    '12:00', '12:30', '14:00', '14:30', '15:00', '15:30',
    '16:00', '16:30', '17:00', '17:30', '18:00', '18:30'
];

// الأوقات غير المتاحة (عشوائية)
const UNAVAILABLE_TIMES = ['10:30', '14:30', '16:00'];

/**
 * إصلاح شامل لنظام حجز الأطباء
 */
function initializeAppointmentSystem() {
    const currentPage = window.location.pathname.split('/').pop();

    switch (currentPage) {
        case 'appointments.html':
            fixAppointmentsPage();
            break;
        case 'doctors.html':
            fixDoctorsPage();
            break;
        case 'booking.html':
            fixBookingPage();
            break;
        case 'confirmation.html':
            fixConfirmationPage();
            break;
        case 'my-appointments.html':
            fixMyAppointmentsPage();
            break;
    }
}

/**
 * إصلاح صفحة اختيار التخصص
 */
function fixAppointmentsPage() {
    console.log('إصلاح صفحة اختيار التخصص...');

    // التأكد من وجود العناصر
    const specialtyCards = document.querySelectorAll('.specialty-card');
    const nextBtn = document.getElementById('nextBtn');

    if (specialtyCards.length === 0) {
        console.error('لم يتم العثور على بطاقات التخصص');
        return;
    }

    let selectedSpecialty = null;

    // إضافة مستمعي الأحداث
    specialtyCards.forEach(card => {
        card.addEventListener('click', function() {
            // إزالة التحديد السابق
            specialtyCards.forEach(c => {
                c.classList.remove('border-primary', 'bg-light');
                c.style.transform = '';
                // إزالة أيقونة الاختيار إن وجدت
                const checkIcon = c.querySelector('.check-icon');
                if (checkIcon) checkIcon.remove();
            });

            // إضافة التحديد الجديد
            this.classList.add('border-primary', 'bg-light');
            this.style.transform = 'scale(1.05)';
            
            // إضافة أيقونة الاختيار
            const checkIcon = document.createElement('div');
            checkIcon.className = 'check-icon position-absolute';
            checkIcon.innerHTML = '<i class="fas fa-check-circle text-primary fs-3"></i>';
            checkIcon.style.cssText = 'top: 10px; right: 10px; z-index: 10;';
            this.appendChild(checkIcon);
            
            selectedSpecialty = this.dataset.specialty;

            // تفعيل زر التالي
            if (nextBtn) {
                nextBtn.disabled = false;
                nextBtn.classList.remove('btn-secondary');
                nextBtn.classList.add('btn-primary');
            }

            console.log('تم اختيار التخصص:', selectedSpecialty);
        });
    });

    // التعامل مع زر التالي
    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            if (selectedSpecialty) {
                console.log('الانتقال إلى صفحة الأطباء للتخصص:', selectedSpecialty);
                window.location.href = `doctors.html?specialty=${selectedSpecialty}`;
            } else {
                showAlert('يرجى اختيار التخصص أولاً', 'warning');
            }
        });
    }
}

/**
 * إصلاح صفحة اختيار الطبيب
 */
function fixDoctorsPage() {
    console.log('إصلاح صفحة اختيار الطبيب...');

    // الحصول على التخصص من URL
    const urlParams = new URLSearchParams(window.location.search);
    const specialty = urlParams.get('specialty') || 'general';

    console.log('التخصص المحدد:', specialty);

    // تحديث العنوان
    const specialtyTitle = document.getElementById('specialtyTitle');
    if (specialtyTitle && SPECIALTIES_DATABASE[specialty]) {
        specialtyTitle.textContent = `أطباء ${SPECIALTIES_DATABASE[specialty].name}`;
    }

    // تحميل الأطباء
    loadDoctorsForSpecialty(specialty);
}

/**
 * تحميل الأطباء للتخصص المحدد
 */
function loadDoctorsForSpecialty(specialty) {
    const doctorsGrid = document.getElementById('doctorsGrid');
    const nextBtn = document.getElementById('nextBtn');

    if (!doctorsGrid) {
        console.error('لم يتم العثور على شبكة الأطباء');
        return;
    }

    const specialtyData = SPECIALTIES_DATABASE[specialty];
    if (!specialtyData) {
        console.error('تخصص غير صحيح:', specialty);
        return;
    }

    const doctors = specialtyData.doctors.map(id => ({
        id,
        ...DOCTORS_DATABASE[id]
    }));

    console.log('تحميل الأطباء:', doctors);

    // إنشاء HTML للأطباء
    doctorsGrid.innerHTML = doctors.map(doctor => `
        <div class="col-md-6 col-lg-4">
            <div class="card doctor-card border-0 shadow-sm rounded-4 h-100" data-doctor="${doctor.id}">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <img src="${doctor.image}" alt="${doctor.name}" class="doctor-image rounded-circle me-3" style="width: 100px; height: 100px; object-fit: cover;">
                        <div>
                            <h5 class="fw-bold mb-1">${doctor.name}</h5>
                            <div class="rating mb-1 text-warning">
                                ${'★'.repeat(Math.floor(doctor.rating))} ${doctor.rating}
                            </div>
                            <small class="text-muted">${doctor.experience} سنة خبرة</small>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span class="fw-bold text-primary">${doctor.price} جنيه</span>
                        <span class="badge bg-success">متاح اليوم</span>
                    </div>
                    <button class="btn btn-outline-primary w-100">اختيار الطبيب</button>
                </div>
            </div>
        </div>
    `).join('');

    // إضافة مستمعي الأحداث
    let selectedDoctor = null;

    document.querySelectorAll('.doctor-card').forEach(card => {
        card.addEventListener('click', function() {
            // إزالة التحديد السابق
            document.querySelectorAll('.doctor-card').forEach(c => {
                c.classList.remove('border-primary', 'bg-light');
                c.style.transform = '';
            });

            // إضافة التحديد الجديد
            this.classList.add('border-primary', 'bg-light');
            this.style.transform = 'scale(1.02)';
            selectedDoctor = this.dataset.doctor;

            // تفعيل زر التالي
            if (nextBtn) {
                nextBtn.disabled = false;
                nextBtn.classList.remove('btn-secondary');
                nextBtn.classList.add('btn-primary');
            }

            console.log('تم اختيار الطبيب:', selectedDoctor);
        });
    });

    // التعامل مع زر التالي
    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            if (selectedDoctor) {
                console.log('الانتقال إلى صفحة الحجز للطبيب:', selectedDoctor);
                window.location.href = `booking.html?doctor=${selectedDoctor}&specialty=${specialty}`;
            } else {
                showAlert('يرجى اختيار الطبيب أولاً', 'warning');
            }
        });
    }
}

/**
 * عرض رسالة تنبيه
 */
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initializeAppointmentSystem);
