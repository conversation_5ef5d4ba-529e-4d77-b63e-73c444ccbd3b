<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختيار الطبيب - مساعد طبي ذكي</title>
    <meta name="description" content="اختر الطبيب المناسب لحجز موعدك">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="style-bootstrap.css">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .doctor-card { transition: all 0.3s ease; cursor: pointer; }
        .doctor-card:hover { transform: translateY(-5px); box-shadow: 0 10px 25px rgba(0,0,0,0.15); }
        .doctor-image { width: 100px; height: 100px; object-fit: cover; }
        .rating { color: #ffc107; }
        .step-indicator { display: flex; justify-content: center; margin-bottom: 2rem; }
        .step { width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 10px; }
        .step.active { background: var(--bs-primary); color: white; }
        .step.completed { background: var(--bs-success); color: white; }
        .step.pending { background: var(--bs-gray-300); color: var(--bs-gray-600); }
        .availability-badge { font-size: 0.8rem; }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top shadow-sm">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="index.html">
                <i class="fas fa-heartbeat text-primary me-2"></i>
                <span class="fw-bold">مساعد طبي ذكي</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="index.html">الرئيسية</a></li>
                    <li class="nav-item"><a class="nav-link" href="diagnosis.html">التشخيص</a></li>
                    <li class="nav-item"><a class="nav-link active" href="appointments.html">حجز موعد</a></li>
                    <li class="nav-item"><a class="nav-link" href="contact.html">اتصل بنا</a></li>

                    <li class="nav-item ms-lg-3 mt-2 mt-lg-0">
                        <button class="btn btn-primary rounded-pill px-4" id="loginBtn">تسجيل الدخول</button>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="py-5 py-md-6 mt-5">
        <div class="container">
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step completed" id="step1">1</div>
                <div class="step active" id="step2">2</div>
                <div class="step pending" id="step3">3</div>
                <div class="step pending" id="step4">4</div>
            </div>

            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="display-4 fw-bold text-gradient mb-3">اختيار الطبيب</h1>
                <p class="lead text-muted" id="specialtyTitle">أطباء متخصصون في التخصص المحدد</p>
            </div>

            <!-- Doctors Grid -->
            <div class="row g-4" id="doctorsGrid">
                <!-- Doctor cards will be populated by JavaScript -->
            </div>

            <!-- Navigation Buttons -->
            <div class="d-flex justify-content-between mt-5">
                <a href="appointments.html" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    السابق
                </a>
                <button class="btn btn-primary" id="nextBtn" disabled>
                    التالي
                    <i class="fas fa-arrow-left ms-2"></i>
                </button>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer bg-dark text-white py-5">
        <div class="container py-4">
            <div class="row g-4">
                <div class="col-md-4 col-lg-3 mb-4">
                    <h5 class="fw-bold mb-3">روابط سريعة</h5>
                    <ul class="list-unstyled">

                        <li class="mb-2"><a href="diagnosis.html" class="link-light text-decoration-none">التشخيص</a></li>
                        <li class="mb-2"><a href="appointments.html" class="link-light text-decoration-none">حجز موعد</a></li>
                    </ul>
                </div>
                <div class="col-md-4 col-lg-3 mb-4">
                    <h5 class="fw-bold mb-3">تواصل معنا</h5>
                    <div class="d-flex gap-3">
                        <a href="#" class="link-light fs-5"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="link-light fs-5"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="link-light fs-5"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
                <div class="col-md-4 col-lg-3 mb-4">
                    <h5 class="fw-bold mb-3">معلومات التواصل</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-envelope me-2"></i><EMAIL></li>
                        <li class="mb-2"><i class="fas fa-phone me-2"></i>+****************</li>
                    </ul>
                </div>
            </div>
            <hr class="my-4 border-light">
            <div class="text-center">
                <p class="mb-0">&copy; 2024 مساعد طبي ذكي. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Arabic Localization -->
    <script src="js/arabic-localization.js"></script>
    <!-- Appointments Fix JS -->
    <script src="js/appointments-fix.js"></script>
    <script>
        let selectedDoctor = null;
        let currentSpecialty = null;
        
        // Sample doctors data
        const doctorsData = {
            cardiology: [
                { id: 1, name: 'د. أحمد محمد', image: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=150&h=150&fit=crop&crop=face', rating: 4.8, experience: 15, price: 300, availability: 'متاح اليوم' },
                { id: 2, name: 'د. فاطمة علي', image: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=150&h=150&fit=crop&crop=face', rating: 4.9, experience: 12, price: 350, availability: 'متاح غداً' },
                { id: 3, name: 'د. محمد حسن', image: 'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=150&h=150&fit=crop&crop=face', rating: 4.7, experience: 18, price: 400, availability: 'متاح اليوم' }
            ],
            neurology: [
                { id: 4, name: 'د. سارة أحمد', image: 'https://images.unsplash.com/photo-1594824475317-d3c5b8b8e8e8?w=150&h=150&fit=crop&crop=face', rating: 4.9, experience: 20, price: 450, availability: 'متاح غداً' },
                { id: 5, name: 'د. عمر محمود', image: 'https://images.unsplash.com/photo-1607990281513-2c110a25bd8c?w=150&h=150&fit=crop&crop=face', rating: 4.6, experience: 14, price: 380, availability: 'متاح اليوم' }
            ],
            orthopedics: [
                { id: 6, name: 'د. ليلى حسام', image: 'https://images.unsplash.com/photo-1594824475317-d3c5b8b8e8e8?w=150&h=150&fit=crop&crop=face', rating: 4.8, experience: 16, price: 320, availability: 'متاح اليوم' },
                { id: 7, name: 'د. كريم فؤاد', image: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=150&h=150&fit=crop&crop=face', rating: 4.7, experience: 11, price: 280, availability: 'متاح غداً' }
            ],
            dermatology: [
                { id: 8, name: 'د. نور الهدى', image: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=150&h=150&fit=crop&crop=face', rating: 4.9, experience: 13, price: 250, availability: 'متاح اليوم' },
                { id: 9, name: 'د. حسام الدين', image: 'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=150&h=150&fit=crop&crop=face', rating: 4.5, experience: 9, price: 220, availability: 'متاح غداً' }
            ],
            pediatrics: [
                { id: 10, name: 'د. منى سالم', image: 'https://images.unsplash.com/photo-1594824475317-d3c5b8b8e8e8?w=150&h=150&fit=crop&crop=face', rating: 4.8, experience: 17, price: 200, availability: 'متاح اليوم' },
                { id: 11, name: 'د. يوسف عادل', image: 'https://images.unsplash.com/photo-1607990281513-2c110a25bd8c?w=150&h=150&fit=crop&crop=face', rating: 4.6, experience: 8, price: 180, availability: 'متاح غداً' }
            ],
            general: [
                { id: 12, name: 'د. رانيا طارق', image: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=150&h=150&fit=crop&crop=face', rating: 4.7, experience: 10, price: 150, availability: 'متاح اليوم' },
                { id: 13, name: 'د. خالد نبيل', image: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=150&h=150&fit=crop&crop=face', rating: 4.4, experience: 7, price: 120, availability: 'متاح غداً' }
            ]
        };

        const specialtyNames = {
            cardiology: 'أمراض القلب',
            neurology: 'أمراض الأعصاب',
            orthopedics: 'العظام',
            dermatology: 'الجلدية',
            pediatrics: 'طب الأطفال',
            general: 'طب عام'
        };
        
        document.addEventListener('DOMContentLoaded', function() {
            // Get specialty from URL
            const urlParams = new URLSearchParams(window.location.search);
            currentSpecialty = urlParams.get('specialty') || 'general';
            
            // Update title
            document.getElementById('specialtyTitle').textContent = `أطباء ${specialtyNames[currentSpecialty]}`;
            
            // Load doctors
            loadDoctors(currentSpecialty);
            
            const nextBtn = document.getElementById('nextBtn');
            nextBtn.addEventListener('click', function() {
                if (selectedDoctor) {
                    window.location.href = `booking.html?doctor=${selectedDoctor}&specialty=${currentSpecialty}`;
                }
            });
        });
        
        function loadDoctors(specialty) {
            const doctorsGrid = document.getElementById('doctorsGrid');
            const doctors = doctorsData[specialty] || [];
            
            doctorsGrid.innerHTML = doctors.map(doctor => `
                <div class="col-md-6 col-lg-4">
                    <div class="card doctor-card border-0 shadow-sm rounded-4 h-100" data-doctor="${doctor.id}">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-center mb-3">
                                <img src="${doctor.image}" alt="${doctor.name}" class="doctor-image rounded-circle me-3">
                                <div>
                                    <h5 class="fw-bold mb-1">${doctor.name}</h5>
                                    <div class="rating mb-1">
                                        ${'★'.repeat(Math.floor(doctor.rating))} ${doctor.rating}
                                    </div>
                                    <small class="text-muted">${doctor.experience} سنة خبرة</small>
                                </div>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span class="fw-bold text-primary">${doctor.price} جنيه</span>
                                <span class="badge bg-success availability-badge">${doctor.availability}</span>
                            </div>
                            <button class="btn btn-outline-primary w-100">اختيار الطبيب</button>
                        </div>
                    </div>
                </div>
            `).join('');
            
            // Add click handlers
            document.querySelectorAll('.doctor-card').forEach(card => {
                card.addEventListener('click', function() {
                    // Remove previous selection
                    document.querySelectorAll('.doctor-card').forEach(c => c.classList.remove('border-primary', 'bg-light'));
                    
                    // Add selection to clicked card
                    this.classList.add('border-primary', 'bg-light');
                    selectedDoctor = this.dataset.doctor;
                    
                    // Enable next button
                    document.getElementById('nextBtn').disabled = false;
                });
            });
        }
    </script>
</body>
</html>
