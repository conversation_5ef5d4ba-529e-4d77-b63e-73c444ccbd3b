/**
 * ملف التعريب الشامل لجميع صفحات المشروع
 * يحول جميع النصوص الإنجليزية إلى العربية تلقائياً
 */

// قاموس الترجمة الشامل
const TRANSLATIONS = {
    // Navigation
    'Home': 'الرئيسية',
    'Diagnosis': 'التشخيص',
    'Book Appointment': 'حجز موعد',
    'About': 'عن المشروع',
    'Login': 'تسجيل الدخول',
    'Register': 'إنشاء حساب',
    
    // Common phrases
    'AI Medical Assistant': 'مساعد طبي ذكي',
    'Your Smart Medical Assistant': 'مساعدك الطبي الذكي',
    'Start Diagnosis': 'ابدأ التشخيص',
    'Get Diagnosis': 'التشخيص',
    'Learn More': 'اعرف المزيد',
    'Back': 'رجوع',
    'Next': 'التالي',
    'Continue': 'متابعة',
    'Submit': 'إرسال',
    'Cancel': 'إلغاء',
    'Save': 'حفظ',
    'Edit': 'تعديل',
    'Delete': 'حذف',
    'Confirm': 'تأكيد',
    'Close': 'إغلاق',
    
    // Forms
    'Email': 'البريد الإلكتروني',
    'Password': 'كلمة المرور',
    'Full Name': 'الاسم الكامل',
    'Phone': 'رقم الهاتف',
    'Age': 'العمر',
    'Gender': 'الجنس',
    'Male': 'ذكر',
    'Female': 'أنثى',
    'Other': 'آخر',
    'Prefer not to say': 'أفضل عدم الإفصاح',
    'Your email': 'بريدك الإلكتروني',
    'Subscribe': 'اشتراك',
    
    // Medical terms
    'Symptoms': 'الأعراض',
    'Describe Your Symptoms': 'اوصف أعراضك',
    'Start Analysis': 'ابدأ التحليل',
    'Analyzing your symptoms': 'جاري تحليل أعراضك',
    'This may take a few seconds': 'قد يستغرق هذا بضع ثوانٍ',
    'Diagnosis Results': 'نتائج التشخيص',
    'Medical Recommendations': 'التوصيات الطبية',
    
    // Symptoms
    'Headache': 'صداع',
    'Fever': 'حمى',
    'Cough': 'سعال',
    'Fatigue': 'إرهاق',
    'Nausea': 'غثيان',
    'Dizziness': 'دوخة',
    'Shortness of breath': 'ضيق في التنفس',
    'Chest pain': 'ألم في الصدر',
    'Back pain': 'ألم في الظهر',
    'Joint pain': 'ألم في المفاصل',
    'Abdominal pain': 'ألم في البطن',
    'Sore throat': 'التهاب الحلق',
    
    // Specialties
    'Cardiology': 'أمراض القلب',
    'Neurology': 'أمراض الأعصاب',
    'Orthopedics': 'العظام',
    'Dermatology': 'الجلدية',
    'Pediatrics': 'طب الأطفال',
    'General Medicine': 'طب عام',
    
    // About page
    'About Us': 'عن المشروع',
    'About the Project': 'عن المشروع',
    'Smart Health System': 'نظام صحي ذكي',
    'Doctor Supervisor': 'الطبيب المشرف',
    'Project Team Members': 'أعضاء فريق المشروع',
    'Project Supervisor': 'مشرف المشروع',
    'Medical AI Specialist': 'متخصص ذكاء اصطناعي طبي',
    'Frontend Developer': 'مطور واجهة أمامية',
    'Backend Developer': 'مطور خلفية',
    'UI/UX Designer': 'مصمم واجهات',
    'AI Specialist': 'متخصص ذكاء اصطناعي',
    'Data Analyst': 'محلل بيانات',
    'Quality Assurance': 'ضمان الجودة',
    'Database Administrator': 'مدير قاعدة البيانات',
    'System Architect': 'مهندس أنظمة',
    'DevOps Engineer': 'مهندس DevOps',
    'About This Project': 'عن هذا المشروع',
    'Technologies Used': 'التقنيات المستخدمة',
    'Key Features': 'الميزات الرئيسية',
    'Symptom Analysis': 'تحليل الأعراض',
    'AI-Powered Diagnosis': 'تشخيص بالذكاء الاصطناعي',
    'User-Friendly Interface': 'واجهة سهلة الاستخدام',
    'Responsive Design': 'تصميم متجاوب',
    
    // Footer
    'Quick Links': 'روابط سريعة',
    'Connect With Us': 'تواصل معنا',
    'Contact': 'معلومات التواصل',
    'Subscribe to Our Newsletter': 'اشترك في نشرتنا الإخبارية',
    'All rights reserved': 'جميع الحقوق محفوظة',
    'Made with': 'صُنع بـ',
    'for better healthcare': 'من أجل رعاية صحية أفضل',
    
    // Messages
    'Send Message': 'إرسال رسالة',
    'Your Name': 'اسمك',
    'Your Message': 'رسالتك',
    'Message': 'الرسالة',
    'Subject': 'الموضوع',
    
    // Appointments
    'Book an Appointment': 'احجز موعد',
    'Choose Specialty': 'اختر التخصص',
    'Choose Doctor': 'اختر الطبيب',
    'Select Date & Time': 'اختر التاريخ والوقت',
    'Patient Information': 'معلومات المريض',
    'Appointment Confirmation': 'تأكيد الموعد',
    'My Appointments': 'مواعيدي',
    'Available Today': 'متاح اليوم',
    'Available Tomorrow': 'متاح غداً',
    'Consultation Fee': 'رسوم الاستشارة',
    'Booking Number': 'رقم الحجز',
    'Appointment Date': 'تاريخ الموعد',
    'Appointment Time': 'وقت الموعد',
    'Doctor Name': 'اسم الطبيب',
    'Patient Name': 'اسم المريض',
    'Status': 'الحالة',
    'Confirmed': 'مؤكد',
    'Pending': 'في الانتظار',
    'Completed': 'مكتمل',
    'Cancelled': 'ملغي',
    
    // Tips and messages
    'Tips for Better Results': 'نصائح للحصول على نتائج أفضل',
    'Be specific about your symptoms': 'كن محدداً في وصف أعراضك',
    'Include when the symptoms started': 'اذكر متى بدأت الأعراض',
    'Mention any relevant medical history': 'اذكر أي تاريخ طبي ذي صلة',
    'Describe the severity of symptoms': 'صف شدة الأعراض',
    'Common Symptoms Reference': 'مرجع الأعراض الشائعة',
    'Click on any symptom to add it to your description': 'انقر على أي عرض لإضافته إلى وصفك',
    
    // Placeholders
    'Example: I\'ve been experiencing headaches and fever for the past 2 days...': 'مثال: أعاني من صداع وحمى منذ يومين...',
    'Enter your symptoms here...': 'أدخل أعراضك هنا...',
    'Your phone number': 'رقم هاتفك',
    'Enter your message here': 'أدخل رسالتك هنا',
    
    // Error messages
    'Please fill in all required fields': 'يرجى ملء جميع الحقول المطلوبة',
    'Please select a date and time': 'يرجى اختيار التاريخ والوقت',
    'Please enter a valid email address': 'يرجى إدخال عنوان بريد إلكتروني صحيح',
    'Please enter a valid phone number': 'يرجى إدخال رقم هاتف صحيح',
    
    // Success messages
    'Appointment booked successfully': 'تم حجز الموعد بنجاح',
    'Message sent successfully': 'تم إرسال الرسالة بنجاح',
    'Registration completed successfully': 'تم التسجيل بنجاح',
    'Login successful': 'تم تسجيل الدخول بنجاح'
};

/**
 * تطبيق التعريب على الصفحة الحالية
 */
function applyArabicLocalization() {
    // تحويل اتجاه الصفحة إلى RTL
    document.documentElement.setAttribute('lang', 'ar');
    document.documentElement.setAttribute('dir', 'rtl');
    
    // إضافة خط Cairo
    if (!document.querySelector('link[href*="Cairo"]')) {
        const link = document.createElement('link');
        link.href = 'https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap';
        link.rel = 'stylesheet';
        document.head.appendChild(link);
        
        // تطبيق الخط على الصفحة
        const style = document.createElement('style');
        style.textContent = 'body { font-family: "Cairo", sans-serif !important; }';
        document.head.appendChild(style);
    }
    
    // إضافة Bootstrap RTL إذا لم يكن موجوداً
    if (!document.querySelector('link[href*="bootstrap.rtl"]')) {
        const rtlLink = document.createElement('link');
        rtlLink.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css';
        rtlLink.rel = 'stylesheet';
        document.head.appendChild(rtlLink);
    }
    
    // ترجمة جميع النصوص
    translatePageContent();
    
    // إصلاح الأيقونات للاتجاه العربي
    fixIconsForRTL();
}

/**
 * ترجمة محتوى الصفحة
 */
function translatePageContent() {
    // ترجمة النصوص المباشرة
    const textNodes = getTextNodes(document.body);
    textNodes.forEach(node => {
        const text = node.textContent.trim();
        if (TRANSLATIONS[text]) {
            node.textContent = TRANSLATIONS[text];
        }
    });
    
    // ترجمة النصوص في العناصر
    Object.keys(TRANSLATIONS).forEach(english => {
        const arabic = TRANSLATIONS[english];
        
        // ترجمة النصوص في العناصر
        const elements = document.querySelectorAll('*');
        elements.forEach(element => {
            if (element.textContent === english) {
                element.textContent = arabic;
            }
            
            // ترجمة placeholder
            if (element.placeholder === english) {
                element.placeholder = arabic;
            }
            
            // ترجمة title
            if (element.title === english) {
                element.title = arabic;
            }
            
            // ترجمة aria-label
            if (element.getAttribute('aria-label') === english) {
                element.setAttribute('aria-label', arabic);
            }
        });
    });
}

/**
 * الحصول على جميع عقد النص
 */
function getTextNodes(element) {
    const textNodes = [];
    const walker = document.createTreeWalker(
        element,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );
    
    let node;
    while (node = walker.nextNode()) {
        if (node.textContent.trim()) {
            textNodes.push(node);
        }
    }
    
    return textNodes;
}

/**
 * إصلاح الأيقونات للاتجاه العربي
 */
function fixIconsForRTL() {
    // تبديل أيقونات الأسهم
    const leftArrows = document.querySelectorAll('.fa-arrow-left');
    leftArrows.forEach(icon => {
        icon.classList.remove('fa-arrow-left');
        icon.classList.add('fa-arrow-right');
    });
    
    const rightArrows = document.querySelectorAll('.fa-arrow-right');
    rightArrows.forEach(icon => {
        icon.classList.remove('fa-arrow-right');
        icon.classList.add('fa-arrow-left');
    });
    
    // إصلاح margin للأيقونات
    const meIcons = document.querySelectorAll('.me-2, .me-3');
    meIcons.forEach(icon => {
        if (icon.classList.contains('me-2')) {
            icon.classList.remove('me-2');
            icon.classList.add('ms-2');
        }
        if (icon.classList.contains('me-3')) {
            icon.classList.remove('me-3');
            icon.classList.add('ms-3');
        }
    });
}

// تطبيق التعريب عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', applyArabicLocalization);

// تطبيق التعريب على المحتوى الديناميكي
const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.type === 'childList') {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    translatePageContent();
                }
            });
        }
    });
});

// مراقبة التغييرات في DOM
observer.observe(document.body, {
    childList: true,
    subtree: true
});
